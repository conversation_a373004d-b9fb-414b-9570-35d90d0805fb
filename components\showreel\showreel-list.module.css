.container {
  margin-top: 20vh;
  min-height: 80vh;
  /* overflow-x: hidden; */
  /* overflow-y: visible; */
  /* margin-bottom: 50vh; */

  @media (max-width: 768px) {
    min-height: 60vh;
  }

  .body {
    margin-left: 10vw;

    h1 {
      margin: 0px;
      margin-top: 10px;
      font-size: 5vw;
      line-height: 5vw;
      text-transform: uppercase;
    }

    p {
      color: white;
      margin: 0px;
      margin-top: 10px;
      font-size: 3vw;
      text-transform: uppercase;

      span {
        position: relative;
      }
    }
  }
  .images {
    display: flex;
    width: 100%;
    justify-content: center;
    position: relative;
    margin-top: 5vh;

    .imageContainer {
      position: absolute;

      img {
        border-radius: 20px;
        object-fit: cover;
        filter: brightness(0.8) grayscale(100%);
      }

      &:nth-of-type(1) {
        width: 800px;
        max-width: 80vw;
        aspect-ratio: 16 / 10;
        z-index: 1;
      }

      &:nth-of-type(2) {
        left: 70vw;
        top: 5vh;
        /* width: 40vh;
        min-width: 200px; */
        width: 30vh;
        min-width: 155px;
        aspect-ratio: 16 / 10;
        z-index: 2;

        @media (max-width: 768px) {
          top: 0vh;
          left: 48vw;
          width: 20vh;
        }
      }

      &:nth-of-type(3) {
        left: 15vw;
        top: 40vh;
        width: 30vh;
        min-width: 165px;
        aspect-ratio: 16 / 10;
        z-index: 3;

        @media (max-width: 768px) {
          left: 5.5vw;
          top: 28vh;
          width: 20vh;
        }
      }

      &:nth-of-type(4) {
        left: 65vw;
        top: 50vh;
        width: 30vh;
        min-width: 150px;
        aspect-ratio: 16 / 10;
        z-index: 3;

        @media (max-width: 768px) {
          left: 55vw;
          top: 30vh;
          width: 15vh;
        }
      }
    }
  }
}
